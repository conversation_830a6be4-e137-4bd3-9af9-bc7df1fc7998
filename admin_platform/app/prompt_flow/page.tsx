'use client'

import { use<PERSON>emo, useState, useCallback, useEffect, useRef } from 'react'
import dayjs from 'dayjs'
import {
  ReactFlow,
  Background,
  Controls,
  ReactFlowProvider,
  useNodesState,
  useEdgesState,
  useReactFlow,
  type Edge,
  type Node
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'

import ModeNode from '@/app/component/prompt_flow/ModeNode'
import InputNode from '@/app/component/prompt_flow/InputNode'
import OutputNode from '@/app/component/prompt_flow/OutputNode'
import type { ExecutionTrace, ModeNodeData } from '@/types/flow'

const MODE_NODE_ID = 'mode-node'
const INPUT_NODE_ID = 'input-node'
const OUTPUT_NODE_ID = 'output-node'

const nodeTypes = {
  modeNode: ModeNode,
  inputNode: InputNode,
  outputNode: OutputNode,
}

const DEFAULT_RANGE = {
  startDate: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD'),
}

const DEFAULT_MODE_STATE: ModeNodeData = {
  mode: 'customerPersona',
  chatId: '',
  chatIds: [],
  timeRange: { ...DEFAULT_RANGE },
  timePoint: 'day1',
  isDanmu: false,
}

interface PersonaRunMeta {
  chatId: string
  portrait: Record<string, unknown>
  rawOutput: string
  chatHistory: unknown
  roundId: string
}

interface PlannerRunMeta {
  chatId: string
  mainTask: string
  context: Record<string, unknown>
  plannerOutput: unknown
  freeKickOutput: unknown
  roundId: string
}

interface MemoryTestRunMeta {
  chatId: string
  memoryTestOutput: Record<string, unknown>
  rawOutput: string
  chatHistory: unknown
  roundId: string
}

function deriveChatIds(modeState: ModeNodeData): string[] {
  if (Array.isArray(modeState.chatIds) && modeState.chatIds.length > 0) {
    return modeState.chatIds
  }
  if (!modeState.chatId) return []
  return modeState.chatId
    .split(/\r?\n/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0)
}

function buildRequestParams(modeState: ModeNodeData) {
  const chatIds = deriveChatIds(modeState)
  if (modeState.mode === 'customerPersona') {
    return {
      chatIds,
      startDate: modeState.timeRange?.startDate,
      endDate: modeState.timeRange?.endDate,
      isDanmu: modeState.isDanmu,
    }
  }

  if (modeState.mode === 'memoryTest') {
    return {
      chatIds,
      customerMemory: modeState.customerMemory,
      startDate: modeState.timeRange?.startDate,
      endDate: modeState.timeRange?.endDate,
    }
  }

  return {
    chatIds,
    timePoint: modeState.timePoint,
    selectedDays: modeState.selectedDays,
    mainTask: modeState.mainTask,
    roundId: modeState.roundId,
    loadExistingPortrait: modeState.loadExistingPortrait,
  }
}

const initialEdges: Edge[] = [
  { id: 'e-mode-input', source: MODE_NODE_ID, target: INPUT_NODE_ID, animated: true },
  { id: 'e-input-output', source: INPUT_NODE_ID, target: OUTPUT_NODE_ID, animated: true },
]

function createNodes(
  modeState: ModeNodeData,
  executionTrace: ExecutionTrace,
  responseMeta: Record<string, unknown>,
  handlers: {
    onModeChange: (patch: Partial<ModeNodeData>) => void
  },
): Node[] {
  const nodes: Node[] = []
  const requestParams = buildRequestParams(modeState)

  nodes.push({
    id: MODE_NODE_ID,
    type: 'modeNode',
    position: { x: 0, y: 0 },
    data: {
      ...modeState,
      onChange: handlers.onModeChange,
    },
  })

  const personaDetails = (responseMeta.personaRuns as PersonaRunMeta[] | undefined)?.map((run) => ({
    chatId: run.chatId,
    chatHistory: run.chatHistory,
  }))

  const plannerDetails = (responseMeta.plannerRuns as PlannerRunMeta[] | undefined)?.map((run) => ({
    chatId: run.chatId,
    context: run.context,
    mainTask: run.mainTask,
  }))

  const memoryTestDetails = (responseMeta.memoryTestRuns as MemoryTestRunMeta[] | undefined)?.map((run) => ({
    chatId: run.chatId,
    chatHistory: run.chatHistory,
    memoryTestOutput: run.memoryTestOutput,
  }))

  const inputDetails = modeState.mode === 'customerPersona'
    ? personaDetails ?? null
    : modeState.mode === 'memoryTest'
      ? memoryTestDetails ?? null
      : plannerDetails ?? null

  nodes.push({
    id: INPUT_NODE_ID,
    type: 'inputNode',
    position: { x: 320, y: 0 },
    data: {
      title: '输入概览',
      params: requestParams,
      description:
        modeState.mode === 'customerPersona'
          ? '按照提供的时间范围提取真实聊天记录，用于客户画像调试。'
          : modeState.mode === 'memoryTest'
            ? '按照提供的时间范围提取真实聊天记录，结合客户记忆和时间信息，用于记忆测试调试。'
            : '聚合客户画像、行为与阶段上下文，用于 Planner + FreeKick 调试。',
      details: inputDetails,
    },
  })

  nodes.push({
    id: OUTPUT_NODE_ID,
    type: 'outputNode',
    position: { x: 760, y: 0 },
    data: {
      trace: executionTrace,
      meta: responseMeta,
    },
  })

  return nodes
}

// 本地存储的键名
const STORAGE_KEYS = {
  MODE_STATE: 'prompt_flow_mode_state',
  EXECUTION_TRACE: 'prompt_flow_execution_trace',
  RESPONSE_META: 'prompt_flow_response_meta',
}

// 从 localStorage 加载状态
function loadFromStorage<T>(key: string, defaultValue: T): T {
  if (typeof window === 'undefined') return defaultValue
  try {
    const stored = localStorage.getItem(key)
    return stored ? JSON.parse(stored) : defaultValue
  } catch {
    return defaultValue
  }
}

// 保存状态到 localStorage
function saveToStorage<T>(key: string, value: T): void {
  if (typeof window === 'undefined') return
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch {
    // 忽略存储错误
  }
}

function FlowInner() {
  const [modeState, setModeState] = useState<ModeNodeData>(() =>
    loadFromStorage(STORAGE_KEYS.MODE_STATE, DEFAULT_MODE_STATE)
  )
  const [executionTrace, setExecutionTrace] = useState<ExecutionTrace>(() =>
    loadFromStorage(STORAGE_KEYS.EXECUTION_TRACE, [])
  )
  const [responseMeta, setResponseMeta] = useState<Record<string, unknown>>(() =>
    loadFromStorage(STORAGE_KEYS.RESPONSE_META, {})
  )
  const [isRunning, setIsRunning] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const { fitView } = useReactFlow()
  const wrapRef = useRef<HTMLDivElement>(null)

  // 检查是否有缓存的数据
  const hasCachedData = executionTrace.length > 0 || Object.keys(responseMeta).length > 0

  const handleModeChange = (patch: Partial<ModeNodeData>) => {
    setModeState((previous) => {
      const next: ModeNodeData = { ...previous, ...patch }
      let modeChanged = false

      if (typeof patch.mode !== 'undefined' && patch.mode !== previous.mode) {
        modeChanged = true
      }

      if (modeChanged) {
        if (next.mode === 'customerPersona') {
          next.timeRange = next.timeRange ?? { ...DEFAULT_RANGE }
          next.timePoint = undefined
          next.mainTask = undefined
          next.customerMemory = undefined
        } else if (next.mode === 'memoryTest') {
          next.timeRange = next.timeRange ?? { ...DEFAULT_RANGE }
          next.timePoint = undefined
          next.mainTask = undefined
          next.customerMemory = next.customerMemory ?? ''
        } else {
          next.timeRange = undefined
          next.timePoint = next.timePoint ?? 'day1'
          next.customerMemory = undefined
        }
        setExecutionTrace([])
        setResponseMeta({})
        // 清空存储的结果
        saveToStorage(STORAGE_KEYS.EXECUTION_TRACE, [])
        saveToStorage(STORAGE_KEYS.RESPONSE_META, {})
      }

      if (patch.timeRange) {
        next.timeRange = { ...previous.timeRange, ...patch.timeRange }
      }

      if (typeof patch.chatIds !== 'undefined') {
        next.chatIds = patch.chatIds
      }

      // 保存模式状态
      saveToStorage(STORAGE_KEYS.MODE_STATE, next)
      return next
    })
  }

  const initialNodes = useMemo(
    () =>
      createNodes(modeState, executionTrace, responseMeta, {
        onModeChange: handleModeChange,
      }),
    [modeState, executionTrace, responseMeta],
  )

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)

  // 当 initialNodes 变化时更新 nodes
  useEffect(() => {
    setNodes(initialNodes)
  }, [initialNodes, setNodes])

  // 进入/退出全屏、或容器尺寸变化时，自动 fitView 让视图合理铺满
  useEffect(() => {
    const handle = () => fitView({ padding: 0.2, duration: 300 })
    const ro = new ResizeObserver(handle)
    if (wrapRef.current) ro.observe(wrapRef.current)

    document.addEventListener('fullscreenchange', handle)
    return () => {
      ro.disconnect()
      document.removeEventListener('fullscreenchange', handle)
    }
  }, [fitView])

  const handleRun = async () => {
    setIsRunning(true)
    setError(null)
    try {
      const requestParams = buildRequestParams(modeState)
      if (!requestParams.chatIds || requestParams.chatIds.length === 0) {
        throw new Error('请至少输入一个 chatId。')
      }

      const body: Record<string, unknown> = {
        mode: modeState.mode,
        params: requestParams,
      }

      const response = await fetch('/api/execute-flow', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      })

      const result = await response.json()
      if (!response.ok) {
        throw new Error(result?.error ?? '执行失败，请稍后重试。')
      }

      const trace = result.trace ?? []
      const meta = result.meta ?? {}

      setExecutionTrace(trace)
      setResponseMeta(meta)

      // 保存执行结果到 localStorage
      saveToStorage(STORAGE_KEYS.EXECUTION_TRACE, trace)
      saveToStorage(STORAGE_KEYS.RESPONSE_META, meta)
    } catch (err) {
      const message = err instanceof Error ? err.message : '执行失败。'
      setError(message)
    } finally {
      setIsRunning(false)
    }
  }



  return (
    <div className="flex h-full flex-col gap-4 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-slate-800">Prompt 调试平台</h1>
          <div className="flex items-center gap-3">
            <p className="text-sm text-slate-500">
              选择场景后拉取真实数据，验证客户画像与 Planner + FreeKick 的完整链路。
            </p>
            {hasCachedData && (
              <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                已缓存结果
              </span>
            )}
          </div>
        </div>
        <div className="flex items-center gap-3">
          {error && <span className="text-sm text-red-600">{error}</span>}
          <button
            type="button"
            className="rounded bg-indigo-600 px-4 py-2 text-sm font-semibold text-white disabled:opacity-60"
            onClick={handleRun}
            disabled={isRunning}
          >
            {isRunning ? '执行中...' : '运行流程'}
          </button>
        </div>
      </div>

      <div ref={wrapRef} className="h-[calc(100vh-200px)] rounded-md border border-slate-200">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          nodeTypes={nodeTypes}
          nodesDraggable={true}
          nodesConnectable={false}
          elementsSelectable={true}
          snapToGrid={false}
          fitView
          className="bg-slate-50"
          proOptions={{ hideAttribution: true }}
        >
          <Background gap={16} color="#CBD5F5" />
          <Controls showInteractive={false} />
        </ReactFlow>
      </div>
    </div>
  )
}

export default function PromptFlowPage() {
  return (
    <ReactFlowProvider>
      <FlowInner />
    </ReactFlowProvider>
  )
}
