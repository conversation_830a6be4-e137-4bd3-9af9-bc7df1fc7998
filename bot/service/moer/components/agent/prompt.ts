import * as hub from 'langchain/hub/node'
import { Runnable } from '@langchain/core/runnables'
import { Config } from '../../../../config/config'

// 设置 LangSmith 环境变量（项目名优先 params，其次配置）
process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName
process.env.LANGCHAIN_TRACING_V2 = 'true'

// 你所有需要管理的 prompt key
type PromptKey = 'router' | 'free-think' | 'free-plan' | 'free-big-plan' | 'test' | 'free-think-moer' | 'free-kick' | 'painter' | 'customer_profile'

// 用于存储已拉取的 prompt 实例
const promptCache: { [key in PromptKey]?: Runnable } = {}

export async function getPrompt(
  name: PromptKey,
  bypassCache = false
): Promise<Runnable> {
  if (!promptCache[name] || bypassCache) {
    // 覆盖或填充缓存，确保后续调用可复用最新版本
    promptCache[name] = await hub.pull(name)
  }
  return promptCache[name]!
}